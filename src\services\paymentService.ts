import { apiRequest } from '@/lib/api/apiClient';

// Stripe Configuration Interface
export interface StripeConfig {
  publishableKey: string;
}

// Payment Intent Interfaces
export interface CreatePaymentIntentPayload {
  userId: string;
  amount: number; // Amount in cents (e.g., 2000 = $20.00)
  currency: string;
  description: string;
}

export interface CreatePaymentIntentResponse {
  paymentIntentId: string;
  clientSecret: string;
  amount: number;
  currency: string;
  status: string;
}

// Stripe Checkout Interfaces
export interface CreateCheckoutSessionPayload {
  billingOptionId: string;
  userId: string;
  successUrl: string;
  cancelUrl: string;
}

export interface CreateCheckoutSessionResponse {
  sessionId: string;
  url: string;
}

// Payment Interfaces (kept for backward compatibility)
export interface CreatePaymentPayload {
  paymentIntentId: string;
  paymentMethodId: string;
  returnUrl: string;
}

export interface PaymentResponse {
  success: boolean;
  paymentIntentId: string;
  status: string;
  message?: string;
  redirectUrl?: string;
}

// Payment Status Interface
export interface PaymentStatus {
  paymentIntentId: string;
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled';
  amount: number;
  currency: string;
  description?: string;
  created: number;
  lastPaymentError?: {
    message: string;
    type: string;
  };
}

// User Payment History Interface
export interface UserPayment {
  paymentIntentId: string;
  amount: number;
  currency: string;
  status: string;
  description: string;
  created: number;
  updated: number;
}

export interface UserPaymentsResponse {
  payments: UserPayment[];
  total: number;
}

// Subscription-related interfaces
export interface SubscriptionPaymentPayload {
  billingOptionId: string;
  userId: string;
  returnUrl?: string;
}

// API Functions

/**
 * Get Stripe configuration (publishable key)
 */
export async function getStripeConfig(): Promise<StripeConfig> {
  return apiRequest('/api/stripe/config', { method: 'GET' });
}

/**
 * Create a payment intent for a subscription
 */
export async function createPaymentIntent(
  payload: CreatePaymentIntentPayload
): Promise<CreatePaymentIntentResponse> {
  return apiRequest('/api/stripe/create-payload', {
    method: 'POST',
    data: payload,
  });
}

/**
 * Process a payment with payment method
 */
export async function createPayment(
  payload: CreatePaymentPayload
): Promise<PaymentResponse> {
  return apiRequest('/api/stripe/create-payment', {
    method: 'POST',
    data: payload,
  });
}

/**
 * Check payment status
 */
export async function checkPaymentStatus(
  paymentIntentId: string
): Promise<PaymentStatus> {
  return apiRequest(`/api/stripe/status/${paymentIntentId}`, {
    method: 'GET',
  });
}

/**
 * Get user's payment history
 */
export async function getUserPayments(
  userId: string
): Promise<UserPaymentsResponse> {
  return apiRequest(`/api/stripe/user/${userId}`, {
    method: 'GET',
  });
}

/**
 * Create subscription payment and redirect to Stripe checkout
 */
export async function createSubscriptionPayment(
  payload: SubscriptionPaymentPayload
): Promise<CreatePaymentIntentResponse> {
  // Map billing options to amounts (in cents)
  const billingAmounts: Record<string, number> = {
    'one-time': 1500, // $15.00
    'monthly': 2900,  // $29.00
    'yearly': 27900,  // $279.00
  };

  const amount = billingAmounts[payload.billingOptionId];
  if (!amount) {
    throw new Error(`Invalid billing option: ${payload.billingOptionId}`);
  }

  const billingDescriptions: Record<string, string> = {
    'one-time': 'UNIV.365 Premium - One-Time Payment',
    'monthly': 'UNIV.365 Premium - Monthly Subscription',
    'yearly': 'UNIV.365 Premium - Yearly Subscription',
  };

  return createPaymentIntent({
    userId: payload.userId,
    amount,
    currency: 'usd',
    description: billingDescriptions[payload.billingOptionId],
  });
}

/**
 * Create Stripe checkout session and redirect user to Stripe
 */
export async function redirectToStripeCheckout(
  billingOptionId: string,
  userId: string
): Promise<void> {
  try {
    // Step 1: Create payment intent
    const paymentIntent = await createSubscriptionPayment({
      billingOptionId,
      userId,
      returnUrl: `${window.location.origin}/dashboard/subscriptions?success=true&session_id={CHECKOUT_SESSION_ID}`
    });

    // Step 2: Create payment with return URL to get redirect URL
    const paymentResult = await createPayment({
      paymentIntentId: paymentIntent.paymentIntentId,
      paymentMethodId: '', // Not needed for redirect flow
      returnUrl: `${window.location.origin}/dashboard/subscriptions?success=true&payment_intent=${paymentIntent.paymentIntentId}`
    });

    // Step 3: Redirect to Stripe if we get a redirect URL
    if (paymentResult.redirectUrl) {
      window.location.href = paymentResult.redirectUrl;
    } else {
      throw new Error('No redirect URL received from payment API');
    }
  } catch (error) {
    console.error('Failed to redirect to Stripe checkout:', error);
    throw error;
  }
}

/**
 * Helper function to format amount from cents to dollars
 */
export function formatAmount(amountInCents: number): string {
  return (amountInCents / 100).toFixed(2);
}

/**
 * Helper function to get billing option details
 */
export function getBillingOptionDetails(billingOptionId: string) {
  const details = {
    'one-time': {
      name: 'One-Time Payment',
      amount: 1500,
      period: 'one-time',
      description: 'Download PDF Document, limited information',
    },
    'monthly': {
      name: 'Monthly Subscription',
      amount: 2900,
      period: 'month',
      description: 'Complete access to the platform',
    },
    'yearly': {
      name: 'Yearly Subscription',
      amount: 27900,
      period: 'year',
      description: 'Complete access with significant savings',
    },
  };

  return details[billingOptionId as keyof typeof details] || null;
}
