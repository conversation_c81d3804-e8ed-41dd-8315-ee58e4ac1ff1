{"info": {"_postman_id": "jwt-mongoapi-collection", "name": "JwtMongoApi Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Test API", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/authapi/test", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["autha<PERSON>", "test"]}}}, {"name": "Sign Up", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"<PERSON>\",\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\",\n  \"roleId\": \"{{roleId}}\"\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/authapi/signup", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["autha<PERSON>", "signup"]}}}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"Password123\"\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/authapi/login", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["autha<PERSON>", "login"]}}}, {"name": "Change User Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{userId}}\",\n  \"roleId\": \"{{roleId}}\"\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/authapi/change-role", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["autha<PERSON>", "change-role"]}}}]}, {"name": "RoleApi Management", "item": [{"name": "Get All Roles", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>"]}}}, {"name": "Get Active Roles", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/active", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "active"]}}}, {"name": "Get Role by ID", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/{{roleId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{roleId}}"]}}}, {"name": "Get Role by Name", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/name/{{roleName}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "name", "{{roleName}}"]}}}, {"name": "Create Role", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Admin\",\n  \"description\": \"Administrator role with full access\"\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>"]}}}, {"name": "Update Role", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"SuperAdmin\",\n  \"description\": \"Super administrator role with enhanced privileges\",\n  \"isActive\": true\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/{{roleId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{roleId}}"]}}}, {"name": "Delete Role", "request": {"method": "DELETE", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/{{roleId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{roleId}}"]}}}, {"name": "Activate Role", "request": {"method": "PATCH", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/{{roleId}}/activate", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{roleId}}", "activate"]}}}, {"name": "Deactivate Role", "request": {"method": "PATCH", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/roleapi/{{roleId}}/deactivate", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "<PERSON><PERSON><PERSON>", "{{roleId}}", "deactivate"]}}}]}, {"name": "ResumeBuilder", "item": [{"name": "Create Resume", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"personal\": {\n    \"fullName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"****** 456 7890\",\n    \"location\": \"St. Louis, MO\",\n    \"linkedIn\": \"https://linkedin.com/in/johndoe\",\n    \"website\": \"https://johndoe.com\"\n  },\n  \"summary\": \"Experienced Salesforce Admin...\",\n  \"skills\": {\n    \"skills\": {\n      \"Programming Languages\": [\"C#\", \"JavaScript\"],\n      \"Frameworks\": [\"React\", \"ASP.NET\"],\n      \"Soft Skills\": [\"Leadership\", \"Teamwork\"]\n    }\n  },\n  \"experience\": [\n    {\n      \"company\": \"Qualcomm\",\n      \"location\": \"USA\",\n      \"title\": \"Salesforce Admin\",\n      \"startDate\": \"Aug 2024\",\n      \"endDate\": \"Present\",\n      \"descriptions\": [\"Configured and maintained...\"]\n    }\n  ],\n  \"education\": [\n    {\n      \"degree\": \"Masters in Information Systems\",\n      \"institution\": \"Saint Louis University\",\n      \"location\": \"St. Louis, MO\",\n      \"startDate\": \"Aug 2023\",\n      \"endDate\": \"May 2025\"\n    }\n  ]\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/resume", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "resume"]}}}, {"name": "Update Resume", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"personal\": {\n    \"fullName\": \"<PERSON>\",\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"****** 456 7890\",\n    \"location\": \"St. Louis, MO\",\n    \"linkedIn\": \"https://linkedin.com/in/johndoe\",\n    \"website\": \"https://johndoe.com\"\n  },\n  \"summary\": \"Updated summary...\",\n  \"skills\": {\n    \"skills\": {\n      \"Programming Languages\": [\"C#\", \"JavaScript\"],\n      \"Frameworks\": [\"React\", \"ASP.NET\"],\n      \"Soft Skills\": [\"Leadership\", \"Teamwork\"]\n    }\n  },\n  \"experience\": [\n    {\n      \"company\": \"Qualcomm\",\n      \"location\": \"USA\",\n      \"title\": \"Salesforce Admin\",\n      \"startDate\": \"Aug 2024\",\n      \"endDate\": \"Present\",\n      \"descriptions\": [\"Configured and maintained...\"]\n    }\n  ],\n  \"education\": [\n    {\n      \"degree\": \"Masters in Information Systems\",\n      \"institution\": \"Saint Louis University\",\n      \"location\": \"St. Louis, MO\",\n      \"startDate\": \"Aug 2023\",\n      \"endDate\": \"May 2025\"\n    }\n  ]\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/resume/{{resumeId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "resume", "{{resumeId}}"]}}}, {"name": "Get All Resumes", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/resume", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "resume"]}}}, {"name": "Get Resume By Id", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/resume/{{resumeId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "resume", "{{resumeId}}"]}}}, {"name": "Delete Resume", "request": {"method": "DELETE", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/resume/{{resumeId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "resume", "{{resumeId}}"]}}}]}, {"name": "Stripe Payment", "item": [{"name": "Get Stripe Config", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/stripe/config", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "stripe", "config"]}}}, {"name": "Create Payment Intent", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"userId\": \"{{userId}}\",\n  \"amount\": 2000,\n  \"currency\": \"usd\",\n  \"description\": \"Test payment for resume builder\"\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/stripe/create-payload", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "stripe", "create-payload"]}}}, {"name": "Create Payment", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"paymentIntentId\": \"{{paymentIntentId}}\",\n  \"paymentMethodId\": \"pm_card_visa\",\n  \"returnUrl\": \"https://example.com/success\"\n}"}, "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/stripe/create-payment", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "stripe", "create-payment"]}}}, {"name": "Check Payment Status", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/stripe/status/{{paymentIntentId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "stripe", "status", "{{paymentIntentId}}"]}}}, {"name": "Get User Payments", "request": {"method": "GET", "header": [], "url": {"raw": "https://university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net/api/stripe/user/{{userId}}", "protocol": "https", "host": ["university-hjevd3cgbwgydzf2.canadacentral-01.azurewebsites.net"], "path": ["api", "stripe", "user", "{{userId}}"]}}}]}], "variable": [{"key": "roleId", "value": "your-role-id-here", "description": "Replace with actual role ID for testing"}, {"key": "<PERSON><PERSON><PERSON>", "value": "Admin", "description": "Replace with actual role name for testing"}, {"key": "userId", "value": "your-user-id-here", "description": "Replace with actual user ID for testing"}, {"key": "resumeId", "value": "your-resume-id-here", "description": "Replace with actual resume ID for testing"}, {"key": "paymentIntentId", "value": "pi_your-payment-intent-id-here", "description": "Replace with actual payment intent ID for testing"}]}