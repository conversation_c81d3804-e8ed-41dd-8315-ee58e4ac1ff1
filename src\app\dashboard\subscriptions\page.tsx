"use client";
import { useState, useEffect } from 'react';
import { Sidebar } from '../_components/Sidebar';
import { TopNav } from '../_components/TopNav';
import { SubscriptionPlans } from './_components/SubscriptionPlans';

export default function SubscriptionsPage() {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [showTestButton, setShowTestButton] = useState(false);

  // Show test button in development mode
  useEffect(() => {
    setShowTestButton(process.env.NODE_ENV === 'development');
  }, []);

  const handleTestAPIs = async () => {
    try {
      const { testPaymentAPIs } = await import('@/utils/testPaymentAPI');
      await testPaymentAPIs();
    } catch (error) {
      console.error('Failed to run API tests:', error);
    }
  };

  return (
    <div className="flex h-screen bg-gray-50">
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />

      <div className="flex-1 flex flex-col overflow-hidden lg:ml-0">
        <TopNav onMenuClick={() => setSidebarOpen(true)} />

        <main className="flex-1 overflow-y-auto p-4 md:p-6">
          {/* Header */}
          <div className="mb-6 md:mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">Subscriptions</h1>
                <p className="text-gray-600 text-sm md:text-base">Choose the perfect plan for your career journey</p>
              </div>
              {showTestButton && (
                <button
                  onClick={handleTestAPIs}
                  className="bg-yellow-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-yellow-600 transition-colors"
                  title="Test Payment APIs (Development Only)"
                >
                  🧪 Test APIs
                </button>
              )}
            </div>
          </div>

          {/* Subscription Plans */}
          <SubscriptionPlans />
        </main>
      </div>
    </div>
  );
}
