"use client";
import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { X, CreditCard, Lock, CheckCircle, AlertCircle, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import * as paymentService from '@/services/paymentService';
import * as subscriptionService from '@/services/subscriptionService';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  billingOptionId: string;
  billingOptionName: string;
  amount: number;
  period: string;
  userId: string;
  onSuccess?: (paymentIntentId: string) => void;
}

type PaymentStep = 'payment-details' | 'processing' | 'success' | 'error';

export function PaymentModal({
  isOpen,
  onClose,
  billingOptionId,
  billingOptionName,
  amount,
  period,
  userId,
  onSuccess
}: PaymentModalProps) {
  const [currentStep, setCurrentStep] = useState<PaymentStep>('payment-details');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [paymentIntentId, setPaymentIntentId] = useState<string | null>(null);

  // Mock payment form state
  const [paymentForm, setPaymentForm] = useState({
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
    email: '',
    saveCard: false
  });

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen) {
      setCurrentStep('payment-details');
      setError(null);
      setPaymentIntentId(null);
      setPaymentForm({
        cardNumber: '',
        expiryDate: '',
        cvv: '',
        cardholderName: '',
        email: '',
        saveCard: false
      });
    }
  }, [isOpen]);

  const handleInputChange = (field: string, value: string | boolean) => {
    setPaymentForm(prev => ({ ...prev, [field]: value }));
  };

  const formatCardNumber = (value: string) => {
    const v = value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    const matches = v.match(/\d{4,16}/g);
    const match = matches && matches[0] || '';
    const parts = [];
    for (let i = 0, len = match.length; i < len; i += 4) {
      parts.push(match.substring(i, i + 4));
    }
    if (parts.length) {
      return parts.join(' ');
    } else {
      return v;
    }
  };

  const formatExpiryDate = (value: string) => {
    const v = value.replace(/\D/g, '');
    if (v.length >= 2) {
      return v.substring(0, 2) + '/' + v.substring(2, 4);
    }
    return v;
  };

  const validateForm = () => {
    if (!paymentForm.cardNumber || paymentForm.cardNumber.replace(/\s/g, '').length < 16) {
      setError('Please enter a valid card number');
      return false;
    }
    if (!paymentForm.expiryDate || paymentForm.expiryDate.length < 5) {
      setError('Please enter a valid expiry date');
      return false;
    }
    if (!paymentForm.cvv || paymentForm.cvv.length < 3) {
      setError('Please enter a valid CVV');
      return false;
    }
    if (!paymentForm.cardholderName.trim()) {
      setError('Please enter the cardholder name');
      return false;
    }
    if (!paymentForm.email.trim() || !paymentForm.email.includes('@')) {
      setError('Please enter a valid email address');
      return false;
    }
    return true;
  };

  const handlePayment = async () => {
    if (!validateForm()) return;

    setLoading(true);
    setError(null);
    setCurrentStep('processing');

    try {
      // Step 1: Create payment intent
      const paymentIntent = await paymentService.createSubscriptionPayment({
        billingOptionId,
        userId,
        returnUrl: window.location.origin + '/dashboard/subscriptions?success=true'
      });

      setPaymentIntentId(paymentIntent.paymentIntentId);

      // Step 2: Simulate payment processing (in real implementation, this would use Stripe Elements)
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Step 3: Process payment (mock success for demo)
      const paymentResult = await paymentService.createPayment({
        paymentIntentId: paymentIntent.paymentIntentId,
        paymentMethodId: 'pm_card_visa', // Mock payment method
        returnUrl: window.location.origin + '/dashboard/subscriptions?success=true'
      });

      if (paymentResult.success) {
        // Step 4: Create subscription record
        await subscriptionService.createSubscription({
          userId,
          planId: billingOptionId,
          paymentIntentId: paymentIntent.paymentIntentId
        });

        setCurrentStep('success');
        onSuccess?.(paymentIntent.paymentIntentId);
      } else {
        throw new Error(paymentResult.message || 'Payment failed');
      }
    } catch (err) {
      console.error('Payment error:', err);
      setError(err instanceof Error ? err.message : 'Payment failed. Please try again.');
      setCurrentStep('error');
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    if (currentStep === 'processing') return; // Prevent closing during processing
    onClose();
  };

  const renderPaymentDetails = () => (
    <div className="space-y-6">
      {/* Order Summary */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h3 className="font-semibold text-gray-900 mb-2">Order Summary</h3>
        <div className="flex justify-between items-center">
          <span className="text-gray-600">{billingOptionName}</span>
          <span className="font-semibold text-gray-900">${amount}/{period}</span>
        </div>
      </div>

      {/* Payment Form */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Email Address
          </label>
          <input
            type="email"
            value={paymentForm.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Card Number
          </label>
          <div className="relative">
            <input
              type="text"
              value={paymentForm.cardNumber}
              onChange={(e) => handleInputChange('cardNumber', formatCardNumber(e.target.value))}
              className="w-full px-3 py-2 pl-10 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="1234 5678 9012 3456"
              maxLength={19}
            />
            <CreditCard className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Expiry Date
            </label>
            <input
              type="text"
              value={paymentForm.expiryDate}
              onChange={(e) => handleInputChange('expiryDate', formatExpiryDate(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="MM/YY"
              maxLength={5}
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              CVV
            </label>
            <input
              type="text"
              value={paymentForm.cvv}
              onChange={(e) => handleInputChange('cvv', e.target.value.replace(/\D/g, ''))}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="123"
              maxLength={4}
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Cardholder Name
          </label>
          <input
            type="text"
            value={paymentForm.cardholderName}
            onChange={(e) => handleInputChange('cardholderName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="John Doe"
          />
        </div>

        <div className="flex items-center">
          <input
            type="checkbox"
            id="saveCard"
            checked={paymentForm.saveCard}
            onChange={(e) => handleInputChange('saveCard', e.target.checked)}
            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
          />
          <label htmlFor="saveCard" className="ml-2 text-sm text-gray-600">
            Save card for future payments
          </label>
        </div>
      </div>

      {error && (
        <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-lg">
          <AlertCircle className="h-5 w-5" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
        <Lock className="h-4 w-4" />
        <span>Your payment information is secure and encrypted</span>
      </div>
    </div>
  );

  const renderProcessing = () => (
    <div className="text-center py-8">
      <Loader2 className="h-12 w-12 text-blue-600 animate-spin mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Processing Payment</h3>
      <p className="text-gray-600">Please wait while we process your payment...</p>
    </div>
  );

  const renderSuccess = () => (
    <div className="text-center py-8">
      <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Successful!</h3>
      <p className="text-gray-600 mb-4">
        Your subscription to {billingOptionName} has been activated.
      </p>
      <button
        onClick={handleClose}
        className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
      >
        Continue
      </button>
    </div>
  );

  const renderError = () => (
    <div className="text-center py-8">
      <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
      <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Failed</h3>
      <p className="text-gray-600 mb-4">{error}</p>
      <div className="space-x-3">
        <button
          onClick={() => setCurrentStep('payment-details')}
          className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Try Again
        </button>
        <button
          onClick={handleClose}
          className="bg-gray-300 text-gray-700 px-6 py-2 rounded-lg hover:bg-gray-400 transition-colors"
        >
          Cancel
        </button>
      </div>
    </div>
  );

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="relative bg-white rounded-2xl shadow-xl max-w-md w-full max-h-[90vh] overflow-y-auto"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h2 className="text-xl font-semibold text-gray-900">
                {currentStep === 'payment-details' && 'Complete Payment'}
                {currentStep === 'processing' && 'Processing...'}
                {currentStep === 'success' && 'Success!'}
                {currentStep === 'error' && 'Payment Failed'}
              </h2>
              {currentStep !== 'processing' && (
                <button
                  onClick={handleClose}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-6 w-6" />
                </button>
              )}
            </div>

            {/* Content */}
            <div className="p-6">
              {currentStep === 'payment-details' && renderPaymentDetails()}
              {currentStep === 'processing' && renderProcessing()}
              {currentStep === 'success' && renderSuccess()}
              {currentStep === 'error' && renderError()}
            </div>

            {/* Footer */}
            {currentStep === 'payment-details' && (
              <div className="px-6 py-4 border-t border-gray-200">
                <button
                  onClick={handlePayment}
                  disabled={loading}
                  className={cn(
                    "w-full py-3 px-4 rounded-lg font-semibold text-white transition-colors",
                    loading
                      ? "bg-gray-400 cursor-not-allowed"
                      : "bg-blue-600 hover:bg-blue-700"
                  )}
                >
                  {loading ? 'Processing...' : `Pay $${amount}`}
                </button>
              </div>
            )}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
