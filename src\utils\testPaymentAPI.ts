// Test utility to verify payment API endpoints
import * as paymentService from '@/services/paymentService';
import * as subscriptionService from '@/services/subscriptionService';

export async function testPaymentAPIs() {
  console.log('🧪 Testing Payment APIs...');
  
  try {
    // Test 1: Get Stripe Config
    console.log('1. Testing Stripe Config...');
    try {
      const config = await paymentService.getStripeConfig();
      console.log('✅ Stripe Config:', config);
    } catch (error) {
      console.log('❌ Stripe Config failed:', error);
    }

    // Test 2: Create Payment Intent
    console.log('2. Testing Payment Intent Creation...');
    try {
      const paymentIntent = await paymentService.createSubscriptionPayment({
        billingOptionId: 'monthly',
        userId: 'test-user-123',
        returnUrl: 'http://localhost:3000/dashboard/subscriptions?success=true'
      });
      console.log('✅ Payment Intent:', paymentIntent);

      // Test 3: Check Payment Status
      console.log('3. Testing Payment Status...');
      try {
        const status = await paymentService.checkPaymentStatus(paymentIntent.paymentIntentId);
        console.log('✅ Payment Status:', status);
      } catch (error) {
        console.log('❌ Payment Status failed:', error);
      }

    } catch (error) {
      console.log('❌ Payment Intent failed:', error);
    }

    // Test 4: Get User Payments
    console.log('4. Testing User Payments...');
    try {
      const userPayments = await paymentService.getUserPayments('test-user-123');
      console.log('✅ User Payments:', userPayments);
    } catch (error) {
      console.log('❌ User Payments failed:', error);
    }

    // Test 5: Fetch Subscription Plans
    console.log('5. Testing Subscription Plans...');
    try {
      const plans = await subscriptionService.fetchSubscriptionPlans();
      console.log('✅ Subscription Plans:', plans);
    } catch (error) {
      console.log('❌ Subscription Plans failed:', error);
    }

    // Test 6: Get User Subscription
    console.log('6. Testing User Subscription...');
    try {
      const userSub = await subscriptionService.getUserSubscription('test-user-123');
      console.log('✅ User Subscription:', userSub);
    } catch (error) {
      console.log('❌ User Subscription failed:', error);
    }

  } catch (error) {
    console.error('❌ Test suite failed:', error);
  }
}

// Helper function to test individual billing options
export async function testBillingOption(billingOptionId: string) {
  console.log(`🧪 Testing billing option: ${billingOptionId}`);
  
  try {
    const details = paymentService.getBillingOptionDetails(billingOptionId);
    console.log('Billing Details:', details);
    
    if (details) {
      const paymentIntent = await paymentService.createSubscriptionPayment({
        billingOptionId,
        userId: 'test-user-123',
        returnUrl: 'http://localhost:3000/dashboard/subscriptions?success=true'
      });
      
      console.log(`✅ Payment Intent for ${billingOptionId}:`, paymentIntent);
      return paymentIntent;
    }
  } catch (error) {
    console.log(`❌ Failed to test ${billingOptionId}:`, error);
  }
}

// Test all billing options
export async function testAllBillingOptions() {
  console.log('🧪 Testing all billing options...');
  
  const billingOptions = ['one-time', 'monthly', 'yearly'];
  
  for (const option of billingOptions) {
    await testBillingOption(option);
    // Add delay between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Mock payment flow test
export async function testMockPaymentFlow(billingOptionId: string) {
  console.log(`🧪 Testing mock payment flow for: ${billingOptionId}`);
  
  try {
    // Step 1: Create payment intent
    const paymentIntent = await paymentService.createSubscriptionPayment({
      billingOptionId,
      userId: 'test-user-123',
      returnUrl: 'http://localhost:3000/dashboard/subscriptions?success=true'
    });
    
    console.log('Step 1 - Payment Intent created:', paymentIntent.paymentIntentId);
    
    // Step 2: Simulate payment processing
    console.log('Step 2 - Processing payment...');
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Step 3: Create payment (mock)
    const paymentResult = await paymentService.createPayment({
      paymentIntentId: paymentIntent.paymentIntentId,
      paymentMethodId: 'pm_card_visa',
      returnUrl: 'http://localhost:3000/dashboard/subscriptions?success=true'
    });
    
    console.log('Step 3 - Payment processed:', paymentResult);
    
    // Step 4: Create subscription
    if (paymentResult.success) {
      const subscription = await subscriptionService.createSubscription({
        userId: 'test-user-123',
        planId: billingOptionId,
        paymentIntentId: paymentIntent.paymentIntentId
      });
      
      console.log('Step 4 - Subscription created:', subscription);
      return subscription;
    }
    
  } catch (error) {
    console.error(`❌ Mock payment flow failed for ${billingOptionId}:`, error);
  }
}

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testPaymentAPIs = testPaymentAPIs;
  (window as any).testBillingOption = testBillingOption;
  (window as any).testAllBillingOptions = testAllBillingOptions;
  (window as any).testMockPaymentFlow = testMockPaymentFlow;
}
