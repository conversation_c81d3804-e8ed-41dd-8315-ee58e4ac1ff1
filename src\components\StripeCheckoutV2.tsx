"use client";
import React, { useState } from "react";
import { loadStripe } from "@stripe/stripe-js";

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface StripeCheckoutV2Props {
  amount: number; // Amount in cents
  userId: string;
  description: string;
  billingOptionId: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  onStart?: () => void;
  children?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const StripeCheckoutV2: React.FC<StripeCheckoutV2Props> = ({
  amount,
  userId,
  description,
  billingOptionId,
  onSuccess,
  onError,
  onStart,
  children,
  className = "",
  disabled = false,
}) => {
  const [loading, setLoading] = useState(false);

  const handleCheckout = async () => {
    if (disabled) return;
    
    setLoading(true);
    onStart?.();

    try {
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("<PERSON>e failed to initialize");
      }

      console.log("Starting checkout process...");

      // Method 1: Try to get a checkout session from the backend
      const success = await tryBackendCheckoutSession(stripe, {
        amount,
        userId,
        description,
        billingOptionId
      });

      if (success) {
        onSuccess?.();
        return;
      }

      // Method 2: If backend doesn't support checkout sessions, show helpful error
      throw new Error(
        "Backend checkout session creation not implemented. " +
        "Please implement a '/api/stripe/create-checkout-session' endpoint that returns a sessionId."
      );

    } catch (err) {
      console.error("Stripe checkout error:", err);
      const errorMessage = err instanceof Error ? err.message : "Payment failed. Please try again.";
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleCheckout}
      disabled={disabled || loading}
      className={className}
    >
      {children || (loading ? "Redirecting to Stripe..." : "Pay with Stripe")}
    </button>
  );
};

// Helper function to try different backend approaches
async function tryBackendCheckoutSession(
  stripe: any,
  params: { amount: number; userId: string; description: string; billingOptionId: string }
): Promise<boolean> {
  const { amount, userId, description, billingOptionId } = params;

  // Try 1: Look for a dedicated checkout session endpoint
  try {
    console.log("Trying dedicated checkout session endpoint...");
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/stripe/create-checkout-session`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          userId,
          amount,
          currency: "usd",
          description,
          billingOptionId,
          successUrl: `${window.location.origin}/dashboard/subscriptions?success=true&session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${window.location.origin}/dashboard/subscriptions?cancelled=true`,
        }),
      }
    );

    if (response.ok) {
      const data = await response.json();
      console.log("Checkout session response:", data);
      
      if (data.sessionId || data.session_id) {
        const sessionId = data.sessionId || data.session_id;
        const { error } = await stripe.redirectToCheckout({ sessionId });
        if (error) throw new Error(error.message);
        return true;
      }
    }
  } catch (error) {
    console.log("Dedicated checkout session endpoint not available:", error);
  }

  // Try 2: Use existing create-payload endpoint and look for session info
  try {
    console.log("Trying existing create-payload endpoint...");
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/stripe/create-payload`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          userId,
          amount,
          currency: "usd",
          description,
        }),
      }
    );

    if (response.ok) {
      const data = await response.json();
      console.log("Create-payload response:", data);
      
      // Check for session ID in various formats
      const sessionId = data.sessionId || data.session_id || data.checkoutSessionId;
      if (sessionId) {
        const { error } = await stripe.redirectToCheckout({ sessionId });
        if (error) throw new Error(error.message);
        return true;
      }
    }
  } catch (error) {
    console.log("Create-payload approach failed:", error);
  }

  // Try 3: Use create-payment endpoint and look for redirect URL
  try {
    console.log("Trying create-payment endpoint for redirect URL...");
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/stripe/create-payment`,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${localStorage.getItem('token')}`,
        },
        body: JSON.stringify({
          paymentIntentId: `temp_${Date.now()}`,
          paymentMethodId: "stripe_checkout",
          returnUrl: `${window.location.origin}/dashboard/subscriptions?success=true&payment_intent=temp`,
        }),
      }
    );

    if (response.ok) {
      const data = await response.json();
      console.log("Create-payment response:", data);
      
      if (data.redirectUrl) {
        console.log("Redirecting to:", data.redirectUrl);
        window.location.href = data.redirectUrl;
        return true;
      }
    }
  } catch (error) {
    console.log("Create-payment approach failed:", error);
  }

  return false;
}

export default StripeCheckoutV2;
