"use client";
import React, { useState } from "react";
import { loadStripe } from "@stripe/stripe-js";

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface StripeCheckoutProps {
  amount: number; // Amount in cents
  userId: string;
  description: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  onStart?: () => void; // Called when payment process starts
  children?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const StripeCheckout: React.FC<StripeCheckoutProps> = ({
  amount,
  userId,
  description,
  onSuccess,
  onError,
  onStart,
  children,
  className = "",
  disabled = false,
}) => {
  const [loading, setLoading] = useState(false);

  const handleCheckout = async () => {
    if (disabled) return;

    setLoading(true);
    onStart?.(); // Notify parent that payment process is starting

    try {
      // Step 1: Create payment intent
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/stripe/create-payload`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${localStorage.getItem('token')}`,
          },
          body: JSON.stringify({
            userId: userId,
            amount: amount,
            currency: "usd",
            description: description,
          }),
        }
      );

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const paymentData = await response.json();
      
      // Step 2: Create checkout session or redirect to Stripe
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Stripe failed to initialize");
      }

      // If the API returns a session ID, use it directly
      if (paymentData.sessionId) {
        const { error } = await stripe.redirectToCheckout({
          sessionId: paymentData.sessionId,
        });

        if (error) {
          throw new Error(error.message);
        }
      } 
      // Otherwise, create a checkout session with line items
      else {
        const { error } = await stripe.redirectToCheckout({
          mode: "payment",
          lineItems: [
            {
              price_data: {
                currency: "usd",
                product_data: {
                  name: description,
                },
                unit_amount: amount,
              },
              quantity: 1,
            },
          ],
          successUrl: `${window.location.origin}/dashboard/subscriptions?success=true&session_id={CHECKOUT_SESSION_ID}`,
          cancelUrl: `${window.location.origin}/dashboard/subscriptions?cancelled=true`,
          clientReferenceId: userId,
        });

        if (error) {
          throw new Error(error.message);
        }
      }

      // If we reach here, the redirect was successful
      onSuccess?.();

    } catch (err) {
      console.error("Stripe checkout error:", err);
      const errorMessage = err instanceof Error ? err.message : "Payment failed. Please try again.";
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleCheckout}
      disabled={disabled || loading}
      className={className}
    >
      {children || (loading ? "Redirecting to Stripe..." : "Pay with Stripe")}
    </button>
  );
};

export default StripeCheckout;
