"use client";
import React, { useState } from "react";
import { loadStripe } from "@stripe/stripe-js";

// Initialize Stripe
const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);

interface StripeCheckoutProps {
  amount: number; // Amount in cents
  userId: string;
  description: string;
  onSuccess?: () => void;
  onError?: (error: string) => void;
  onStart?: () => void; // Called when payment process starts
  children?: React.ReactNode;
  className?: string;
  disabled?: boolean;
}

const StripeCheckout: React.FC<StripeCheckoutProps> = ({
  amount,
  userId,
  description,
  onSuccess,
  onError,
  onStart,
  children,
  className = "",
  disabled = false,
}) => {
  const [loading, setLoading] = useState(false);

  const handleCheckout = async () => {
    if (disabled) return;

    setLoading(true);
    onStart?.(); // Notify parent that payment process is starting

    try {
      // Step 1: Initialize Stripe
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("<PERSON>e failed to initialize");
      }

      // Step 2: Try to create payment intent first (for session ID)
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/stripe/create-payload`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${localStorage.getItem('token')}`,
            },
            body: JSON.stringify({
              userId: userId,
              amount: amount,
              currency: "usd",
              description: description,
            }),
          }
        );

        if (response.ok) {
          const paymentData = await response.json();
          console.log("Payment API response:", paymentData);

          // If the API returns a session ID, use it directly
          if (paymentData.sessionId) {
            const { error } = await stripe.redirectToCheckout({
              sessionId: paymentData.sessionId,
            });

            if (error) {
              throw new Error(error.message);
            }
            return; // Exit if successful
          }

          // Check if we have a client secret for payment intent
          if (paymentData.clientSecret) {
            console.log("Got client secret, but need session ID for checkout");
          }
        }
      } catch (apiError) {
        console.warn("API payment intent creation failed, trying direct approach:", apiError);
      }

      // Step 3: Fallback - try to create payment with the existing API
      try {
        const paymentResponse = await fetch(
          `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/stripe/create-payment`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${localStorage.getItem('token')}`,
            },
            body: JSON.stringify({
              paymentIntentId: "temp_" + Date.now(), // Temporary ID
              paymentMethodId: "stripe_checkout",
              returnUrl: `${window.location.origin}/dashboard/subscriptions?success=true&session_id={CHECKOUT_SESSION_ID}`,
            }),
          }
        );

        if (paymentResponse.ok) {
          const paymentResult = await paymentResponse.json();
          console.log("Payment result:", paymentResult);

          // If we get a redirect URL, use it
          if (paymentResult.redirectUrl) {
            console.log("Redirecting to:", paymentResult.redirectUrl);
            window.location.href = paymentResult.redirectUrl;
            return;
          }
        } else {
          const errorData = await paymentResponse.text();
          console.error("Payment API error:", paymentResponse.status, errorData);
        }
      } catch (paymentError) {
        console.warn("Payment API failed:", paymentError);
      }

      // Step 4: Final fallback - provide helpful error message
      throw new Error(
        "Unable to create payment session. " +
        "The backend needs to implement Stripe Checkout Session creation. " +
        "Please check the console for API response details."
      );

    } catch (err) {
      console.error("Stripe checkout error:", err);
      const errorMessage = err instanceof Error ? err.message : "Payment failed. Please try again.";
      onError?.(errorMessage);

    } catch (err) {
      console.error("Stripe checkout error:", err);
      const errorMessage = err instanceof Error ? err.message : "Payment failed. Please try again.";
      onError?.(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleCheckout}
      disabled={disabled || loading}
      className={className}
    >
      {children || (loading ? "Redirecting to Stripe..." : "Pay with Stripe")}
    </button>
  );
};

export default StripeCheckout;
